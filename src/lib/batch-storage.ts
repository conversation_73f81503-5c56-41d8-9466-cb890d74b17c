/**
 * 批量操作数据存储管理
 * 用于处理大量robotIds的存储和读取，避免URL长度限制
 */

export interface BatchData {
  robotIds: string[];
  timestamp: number;
  agentInfo: {
    province: string;
    city: string;
    area: string;
    name: string;
    id: string;
    stageType: string;
  };
}

export class BatchStorageManager {
  private static readonly PREFIX = 'robotBatch_';
  private static readonly EXPIRY_TIME = 60 * 60 * 1000; // 1小时

  /**
   * 存储批量数据
   * @param robotIds 机器人ID列表
   * @param agentInfo 代理商信息
   * @returns 批量操作ID
   */
  static storeBatchData(robotIds: string[], agentInfo: BatchData['agentInfo']): string {
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const batchData: BatchData = {
      robotIds,
      timestamp: Date.now(),
      agentInfo
    };

    try {
      const key = `${this.PREFIX}${batchId}`;
      sessionStorage.setItem(key, JSON.stringify(batchData));
      console.log(`Stored batch data for ${robotIds.length} robots with ID: ${batchId}`);
      
      // 清理过期数据
      this.cleanExpiredData();
      
      return batchId;
    } catch (error) {
      console.error('Failed to store batch data in sessionStorage:', error);
      throw new Error('存储批量数据失败');
    }
  }

  /**
   * 读取批量数据
   * @param batchId 批量操作ID
   * @returns 批量数据或null
   */
  static getBatchData(batchId: string): BatchData | null {
    try {
      const key = `${this.PREFIX}${batchId}`;
      const batchDataStr = sessionStorage.getItem(key);
      
      if (!batchDataStr) {
        console.warn(`No batch data found for batchId: ${batchId}`);
        return null;
      }

      const batchData: BatchData = JSON.parse(batchDataStr);
      
      // 检查是否过期
      if (Date.now() - batchData.timestamp > this.EXPIRY_TIME) {
        console.log('Batch data expired, removing...');
        sessionStorage.removeItem(key);
        return null;
      }

      console.log(`Loaded batch data for ${batchData.robotIds.length} robots from sessionStorage`);
      return batchData;
    } catch (error) {
      console.error('Failed to load batch data from sessionStorage:', error);
      return null;
    }
  }

  /**
   * 删除批量数据
   * @param batchId 批量操作ID
   */
  static removeBatchData(batchId: string): void {
    try {
      const key = `${this.PREFIX}${batchId}`;
      sessionStorage.removeItem(key);
      console.log(`Removed batch data for batchId: ${batchId}`);
    } catch (error) {
      console.error('Failed to remove batch data:', error);
    }
  }

  /**
   * 清理所有过期的批量数据
   */
  static cleanExpiredData(): void {
    try {
      const keysToRemove: string[] = [];
      
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith(this.PREFIX)) {
          const batchDataStr = sessionStorage.getItem(key);
          if (batchDataStr) {
            try {
              const batchData: BatchData = JSON.parse(batchDataStr);
              if (Date.now() - batchData.timestamp > this.EXPIRY_TIME) {
                keysToRemove.push(key);
              }
            } catch (error) {
              // 如果解析失败，也删除这个数据
              keysToRemove.push(key);
            }
          }
        }
      }

      keysToRemove.forEach(key => {
        sessionStorage.removeItem(key);
        console.log(`Removed expired batch data: ${key}`);
      });

      if (keysToRemove.length > 0) {
        console.log(`Cleaned ${keysToRemove.length} expired batch data entries`);
      }
    } catch (error) {
      console.error('Failed to clean expired batch data:', error);
    }
  }

  /**
   * 获取当前存储的批量数据统计
   */
  static getStorageStats(): { count: number; totalRobots: number } {
    let count = 0;
    let totalRobots = 0;

    try {
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && key.startsWith(this.PREFIX)) {
          const batchDataStr = sessionStorage.getItem(key);
          if (batchDataStr) {
            try {
              const batchData: BatchData = JSON.parse(batchDataStr);
              count++;
              totalRobots += batchData.robotIds.length;
            } catch (error) {
              // 忽略解析错误的数据
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to get storage stats:', error);
    }

    return { count, totalRobots };
  }
}

// 页面卸载时清理数据（可选）
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    BatchStorageManager.cleanExpiredData();
  });
}
