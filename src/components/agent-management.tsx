"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AgentRow } from '@/components/ui/AgentRow';

import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { courseService } from "@/services/course";
import { ApiClientError } from "@/services/api";
import { toastService } from "@/services/toast";

// 定义代理商数据类型
export interface AgentData {
  id?: string;
  companyName: string;
  location: string;
  robotCount: string;
  snLost?: boolean;
  // 保存代理商的具体信息，用于传递到详情页面
  agentInfo?: {
    agentId: number;
    agentName: string;
    province: string;
    city: string;
    area: string;
    bots: number; // 机器人台数
  };
}

interface AgentManagementProps {
  agentData: AgentData[];
  locationType?: number;
}

export function AgentManagement({ agentData, locationType = 0 }: AgentManagementProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  
  // 弹窗状态（保留确认对话框相关状态）

  // 确认对话框状态
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [confirmDialogProps, setConfirmDialogProps] = useState({
    title: "",
    message: "",
    onConfirm: () => {},
  });

  // 授权操作状态管理
  const [authorizingAgents, setAuthorizingAgents] = useState<Record<string, boolean>>({});

  // 处理一键授权/关闭
  const handleBatchAuthorization = async (agent: AgentData, enable: boolean) => {
    if (!agent.agentInfo) {
      toastService.error('缺少代理商信息，无法进行授权操作');
      return;
    }

    const agentKey = agent.id || `${agent.agentInfo.province}-${agent.agentInfo.city}-${agent.agentInfo.agentName}`;

    try {
      setAuthorizingAgents(prev => ({ ...prev, [agentKey]: true }));

      const params = {
        province: agent.agentInfo.province,
        city: agent.agentInfo.city,
        area: agent.agentInfo.area,
        agenId: agent.agentInfo.agentId, // API文档中的字段名
        stageType: 'UNKNOWN' as const // 默认值，可以根据需要调整
        // 注意：一键操作不传 courseId，表示对所有课程进行操作
      };

      if (enable) {
        await courseService.enableAgentCourse(params);
        toastService.success(`${agent.agentInfo.agentName} 一键授权成功`);
      } else {
        await courseService.disableAgentCourse(params);
        toastService.success(`${agent.agentInfo.agentName} 一键关闭成功`);
      }

    } catch (error) {
      const errorMessage = error instanceof ApiClientError
        ? error.message
        : `${agent.agentInfo.agentName} 一键${enable ? '授权' : '关闭'}失败`;
      toastService.error(errorMessage);
      console.error(`Failed to batch ${enable ? 'enable' : 'disable'} courses for agent ${agent.agentInfo.agentName}:`, error);
    } finally {
      setAuthorizingAgents(prev => ({ ...prev, [agentKey]: false }));
    }
  };

  const currentAgents = agentData

  // 处理按钮点击事件
  const handleButtonClick = (agent: AgentData, buttonLabel: string) => {
    // 获取当前页面的所有查询参数（提取到外层避免重复声明）
    const province = searchParams.get('province');
    const city = searchParams.get('city');
    const county = searchParams.get('county');
    const query = searchParams.get('query');
    const stageType = searchParams.get('stageType');

    switch (buttonLabel) {
      case '补全信息':
        // 构建参数
        const completeParams = new URLSearchParams();

        if (province) completeParams.set('province', province);
        if (city) completeParams.set('city', city);
        if (county) completeParams.set('county', county);
        if (query) completeParams.set('query', query);
        if (stageType) completeParams.set('stageType', stageType);

        // 添加代理商特定参数
        if (agent.id) completeParams.set('id', agent.id);
        completeParams.set('action', 'complete');

        // 跳转到信息补全页面，携带所有参数
        router.push(`/courseware/robot/nobelong?${completeParams.toString()}`);
        break;
      case '编辑归属':
        // 构建参数 - 传递代理商的具体信息，参考精细管理的逻辑
        const editParams = new URLSearchParams();

        if (agent.agentInfo) {
          // 使用代理商的具体信息
          editParams.set('agentId', agent.agentInfo.agentId.toString());
          editParams.set('agentProvince', agent.agentInfo.province);
          editParams.set('agentCity', agent.agentInfo.city);
          editParams.set('agentArea', agent.agentInfo.area);
          editParams.set('agentName', agent.agentInfo.agentName);
          editParams.set('agentBots', agent.agentInfo.bots.toString());
        } else {
          // 降级处理：使用组合ID
          if (agent.id) editParams.set('agentCompositeId', agent.id);
        }

        // 传递搜索状态的 stageType（用于API调用）
        if (stageType) editParams.set('stageType', stageType);

        // 添加操作标识，表示这是编辑归属操作
        editParams.set('action', 'editBelong');

        // 跳转到机器人列表页面进行编辑归属
        router.push(`/courseware/robot/list?${editParams.toString()}`);
        break;
      case '精细管理':
        // 构建参数 - 传递代理商的具体信息，避免与搜索状态冲突
        const detailParams = new URLSearchParams();

        if (agent.agentInfo) {
          // 使用代理商的具体信息
          detailParams.set('agentId', agent.agentInfo.agentId.toString());
          detailParams.set('agentProvince', agent.agentInfo.province);
          detailParams.set('agentCity', agent.agentInfo.city);
          detailParams.set('agentArea', agent.agentInfo.area);
          detailParams.set('agentName', agent.agentInfo.agentName);
          detailParams.set('agentBots', agent.agentInfo.bots.toString());
        } else {
          // 降级处理：使用组合ID
          if (agent.id) detailParams.set('agentCompositeId', agent.id);
        }

        // 传递搜索状态的 stageType（用于API调用）
        if (stageType) detailParams.set('stageType', stageType);

        // 添加操作标识
        detailParams.set('action', 'detail');

        // 跳转到精细管理页面
        router.push(`/courseware/agent/detail?${detailParams.toString()}`);
        break;
      case '一键授权':
        // 处理一键授权逻辑
        setConfirmDialogProps({
          title: "确认授权",
          message: `确定要对 ${agent.location} 的所有课程进行授权吗？`,
          onConfirm: () => {
            handleBatchAuthorization(agent, true);
            setIsConfirmDialogOpen(false);
          }
        });
        setIsConfirmDialogOpen(true);
        break;
      case '一键关闭':
        // 处理一键关闭逻辑
        setConfirmDialogProps({
          title: "确认关闭",
          message: `确定要对 ${agent.location} 的所有课程进行关闭吗？`,
          onConfirm: () => {
            handleBatchAuthorization(agent, false);
            setIsConfirmDialogOpen(false);
          }
        });
        setIsConfirmDialogOpen(true);
        break;
      default:
        console.log('未知按钮', buttonLabel, agent);
    }
  };



  // 根据代理商数据生成展示所需的属性
  const getAgentDisplayProps = (agent: AgentData) => {
    // 默认按钮
    const defaultButtons = [
      { label: '一键授权', color: 'green' as const },
      { label: '一键关闭', color: 'red' as const }
    ];
    
    // 根据条件确定头像类型和第一个按钮
    if (!agent.companyName && agent.snLost === true) {
      // 情况1: 无公司名称且SN丢失
      return {
        avatarType: 'red' as const,
        displayName: '< 暂无信息 >',
        actionButtons: [
          { label: '补全信息', color: 'orange' as const },
          ...defaultButtons
        ]
      };
    } else if (!agent.companyName && agent.snLost === false) {
      // 情况2: 无公司名称但SN未丢失
      return {
        avatarType: 'orange' as const,
        displayName: '< 暂无归属代理商 >',
        actionButtons: [
          { label: '编辑归属', color: 'orange' as const },
          ...defaultButtons
        ]
      };
    } else {
      // 情况3: 有公司名称
      return {
        avatarType: 'gray' as const,
        displayName: agent.companyName,
        actionButtons: [
          { label: '精细管理', color: 'blue' as const },
          ...defaultButtons
        ]
      };
    }
  };

  return (
    <div className="space-y-4">
      {/* 代理商列表 */}
      <div className="space-y-2">
        {currentAgents.map((agent, index) => {
          const displayProps = getAgentDisplayProps(agent);
          const agentKey = agent.id || `${agent.agentInfo?.province}-${agent.agentInfo?.city}-${agent.agentInfo?.agentName}`;
          const isAuthorizing = authorizingAgents[agentKey] || false;

          return (
            <AgentRow
              key={index}
              avatarType={displayProps.avatarType}
              companyName={displayProps.displayName}
              locationType={locationType}
              location={agent.location}
              robotCount={agent.robotCount}
              actionButtons={displayProps.actionButtons.map(button => ({
                ...button,
                onClick: () => handleButtonClick(agent, button.label),
                loading: isAuthorizing && (button.label === '一键授权' || button.label === '一键关闭'),
                disabled: isAuthorizing
              }))}
            />
          );
        })}
      </div>

      
      {/* 确认对话框 */}
      <ConfirmDialog
        open={isConfirmDialogOpen}
        onOpenChange={setIsConfirmDialogOpen}
        title={confirmDialogProps.title}
        message={confirmDialogProps.message}
        onConfirm={confirmDialogProps.onConfirm}
        confirmText="确定"
      />
    </div>
  );
}
