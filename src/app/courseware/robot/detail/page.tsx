"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/app-layout";
import { RobotInfoCard } from "@/components/robot-info-card";
import { getSnCourse } from "@/services/sn";
import { CourseGrade } from "@/types/api";
import { toastService } from "@/services/toast";
import { agentService } from "@/services/agent";

interface RobotInfo {
  sn: string;
  id: string;
  company: string;
  location: string;
  isLocked: boolean;
  agentId?: number; // 添加agentId字段
  province?: string; // 添加省份字段
  city?: string; // 添加城市字段
  area?: string; // 添加区域字段
  courses: Array<{
    id: string;
    name: string;
    authorized: boolean;
  }>;
}

export default function RobotDetailPage() {
  const searchParams = useSearchParams();
  const [robotInfo, setRobotInfo] = useState<RobotInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const sn = searchParams.get('sn');
    
    if (!sn) {
      toastService.error('缺少SN参数');
      setLoading(false);
      return;
    }

    // 加载SN课程信息
    loadSnCourseInfo(sn);
  }, [searchParams]);

  const loadSnCourseInfo = async (sn: string) => {
    try {
      setLoading(true);

      // 并行调用三个接口
      const [courses, agentDetail, agentInfo] = await Promise.allSettled([
        // 调用 /sn/course 接口获取课程信息
        getSnCourse({ sn }),
        // 调用 /agent/detail 接口获取SN、ID、地址信息
        agentService.getAgentDetail({ sn }),
        // 调用 /agent/find 接口获取合作商名称信息
        agentService.findAgents({ sn })
      ]);

      // 处理课程信息
      let formattedCourses: Array<{id: string; name: string; authorized: boolean}> = [];
      if (courses.status === 'fulfilled') {
        formattedCourses = courses.value.map((course: CourseGrade) => ({
          id: course.id?.toString() || '',
          name: course.gradeName || '',
          authorized: course.status === 1, // 假设status为1表示已授权
        }));
      } else {
        console.error('获取课程信息失败:', courses.reason);
        toastService.error('获取课程信息失败');
      }

      // 初始化机器人信息变量
      let robotSn = sn; // 默认使用传入的SN
      let robotId = sn; // 默认使用传入的SN作为ID
      let company = "";
      let location = "";
      let agentId: number | undefined = undefined;
      let province = "";
      let city = "";
      let area = "";

      // 从 /agent/detail 获取SN、ID、地址信息
      if (agentDetail.status === 'fulfilled') {
        const agent = agentDetail.value;
        if (agent.orderSnLocations && agent.orderSnLocations.length > 0) {
          // 查找匹配的SN记录，或使用第一个记录
          const matchingLocation = agent.orderSnLocations.find(loc => loc.sn === sn)
            || agent.orderSnLocations[0];

          // 字段1: SN - 从 /agent/detail 接口获取
          robotSn = matchingLocation.sn || sn;

          // 字段2: ID - 从 /agent/detail 接口获取 robotId
          robotId = matchingLocation.robotId || sn;

          // 字段4: 地址 - 从 /agent/detail 接口获取省市区信息组合
          province = matchingLocation.province || "";
          city = matchingLocation.city || "";
          area = matchingLocation.area || "";
          location = [province, city, area]
            .filter(Boolean)
            .join(' ');
        }
      } else {
        console.error('获取代理商详情信息失败:', agentDetail.reason);
      }

      // 字段3: 合作商名称 - 从 /agent/find 获取
      if (agentInfo.status === 'fulfilled') {
        const agents = agentInfo.value;
        if (agents && agents.length > 0) {
          // 如果有多个代理商记录，取第一个
          const agent = agents[0];
          company = agent.agentName;
          agentId = agent.agentId; // 保存agentId

          // 如果从 /agent/detail 没有获取到位置信息，可以从这里获取作为备用
          if (!location) {
            province = agent.province || "";
            city = agent.city || "";
            area = agent.area || "";
            location = [province, city, area]
              .filter(Boolean)
              .join(' ');
          }
        }
      } else {
        console.error('获取合作商名称信息失败:', agentInfo.reason);
      }

      // 构建机器人信息对象
      const robotData: RobotInfo = {
        sn: robotSn,        // 字段1: 从 /agent/detail 获取
        id: robotId,        // 字段2: 从 /agent/detail 获取 robotId
        company: company,   // 字段3: 从 /agent/find 获取 agentName
        location: location, // 字段4: 从 /agent/detail 获取地址组合
        isLocked: false,    // 暂时为false，可以后续从其他接口获取
        agentId: agentId,   // 从 /agent/find 获取的 agentId
        province: province, // 省份信息
        city: city,         // 城市信息
        area: area,         // 区域信息
        courses: formattedCourses,
      };

      setRobotInfo(robotData);
    } catch (error) {
      console.error('加载机器人信息失败:', error);
      toastService.error('加载机器人信息失败');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-white">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  if (!robotInfo) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-white">未找到机器人信息</div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <RobotInfoCard
          robotInfo={robotInfo}
          province={robotInfo.province} // 使用保存的省份信息
          city={robotInfo.city} // 使用保存的城市信息
          area={robotInfo.area} // 使用保存的区域信息
          agentId={robotInfo.agentId?.toString()} // 使用从/agent/find获取的agentId
          stageType="PRIMARY" // 默认为PRIMARY，可以根据需要调整
        />
      </div>
    </AppLayout>
  );
}
