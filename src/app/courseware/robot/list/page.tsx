"use client";

import { useState, useEffect } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { AppLayout } from "@/components/app-layout";
import { RobotsTable } from "@/components/robots-table";
import { AgentIcon } from "@/components/ui/agent-icon";
import { MapPin } from "lucide-react";
import { RobotIcon } from "@/components/ui/robot-icon";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { CompanyEditDialog } from "@/components/company-edit-dialog";
import { agentService } from "@/services/agent";
import { courseService } from "@/services/course";
import { ApiClientError } from "@/services/api";
import { toastService } from "@/services/toast";
import { OrderSnLocation } from "@/types/api";
import { BatchStorageManager } from "@/lib/batch-storage";

// 机器人数据类型
interface Robot {
  id: string;
  sn: string;
  location: string;
  lastContact: string;
  selected?: boolean;
}

// 数据转换函数：将 OrderSnLocation 转换为 Robot
const convertOrderSnLocationToRobot = (orderSn: OrderSnLocation): Robot => {
  return {
    id: orderSn.robotId,
    sn: orderSn.sn || '-',
    location: [orderSn.province, orderSn.city, orderSn.area].filter(Boolean).join(''),
    lastContact: '-', // API没有提供这个字段，设置默认值
    selected: false
  };
};

export default function RobotsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLocked, setIsLocked] = useState(false);
  const [selectedRobots, setSelectedRobots] = useState<Record<string, boolean>>({});
  const [isCompanyDialogOpen, setIsCompanyDialogOpen] = useState(false);
  const [locationType, setLocationType] = useState(0);

  // 批量授权状态管理
  const [batchAuthorizing, setBatchAuthorizing] = useState(false);

  // 机器人数据状态
  const [robots, setRobots] = useState<Robot[]>([]);
  const [totalRobots, setTotalRobots] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 代理商信息状态
  const [agentInfo, setAgentInfo] = useState({
    company: '',
    location: '',
    totalRobots: 0
  });

  // 从URL参数加载机器人数据
  useEffect(() => {
    const loadRobotsData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 从URL获取参数
        const agentProvince = searchParams.get('agentProvince') || '';
        const agentCity = searchParams.get('agentCity') || '';
        const agentArea = searchParams.get('agentArea') || '';
        const agentName = searchParams.get('agentName') || '';
        const agentBots = searchParams.get('agentBots') || '';
        const agentId = searchParams.get('agentId') || '';
        const stageType = searchParams.get('stageType') as 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY' || 'UNKNOWN';

        // 更新代理商信息显示
        setAgentInfo({
          company: agentName,
          location: `${agentProvince} ${agentCity}`.trim(),
          totalRobots: agentBots ? parseInt(agentBots) : 0
        });

        // 构建API请求参数
        const searchReq = {
          province: agentProvince || undefined,
          city: agentCity || undefined,
          area: agentArea || undefined,
          agenId: agentId ? parseInt(agentId) : undefined,
          agentName: agentName || undefined,
          stageType: stageType
        };

        console.log('Loading robots with params:', searchReq);

        // 调用API获取机器人详细信息
        const agentDetail = await agentService.getAgentDetail(searchReq);

        // 转换数据格式
        const convertedRobots = agentDetail.orderSnLocations.map(convertOrderSnLocationToRobot);
        setRobots(convertedRobots);
        setTotalRobots(convertedRobots.length);

        console.log('Loaded robots:', convertedRobots);

      } catch (error) {
        const errorMessage = error instanceof ApiClientError
          ? error.message
          : '加载机器人数据失败';
        setError(errorMessage);
        toastService.error(errorMessage);
        console.error('Failed to load robots:', error);
      } finally {
        setLoading(false);
      }
    };

    loadRobotsData();
  }, [searchParams]);

  // 处理单个选择变化
  const handleSelectChange = (robotId: string, selected: boolean) => {
    setSelectedRobots(prev => ({ ...prev, [robotId]: selected }));
  };

  // 处理全选变化
  const handleSelectAll = (selected: boolean) => {
    const newSelectedRobots = { ...selectedRobots };
    robots.forEach(robot => {
      newSelectedRobots[robot.id] = selected;
    });
    setSelectedRobots(newSelectedRobots);
  };

  // 获取选中的机器人ID列表
  const getSelectedRobotIds = (): string[] => {
    return Object.entries(selectedRobots)
      .filter(([_, selected]) => selected)
      .map(([robotId, _]) => robotId);
  };

  // 处理一键授权/关闭
  const handleBatchAuthorization = async (enable: boolean) => {
    const selectedRobotIds = getSelectedRobotIds();

    if (selectedRobotIds.length === 0) {
      toastService.error('请先选择要操作的机器人');
      return;
    }

    // 从URL获取必要参数
    const agentProvince = searchParams.get('agentProvince') || '';
    const agentCity = searchParams.get('agentCity') || '';
    const agentArea = searchParams.get('agentArea') || '';
    const agentId = searchParams.get('agentId') || '';
    const stageType = searchParams.get('stageType') as 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY' || 'UNKNOWN';

    if (!agentProvince || !agentCity || !agentId) {
      toastService.error('缺少必要的授权参数');
      return;
    }

    try {
      setBatchAuthorizing(true);

      const params = {
        province: agentProvince,
        city: agentCity,
        area: agentArea,
        agenId: agentId ? parseInt(agentId) : undefined, // API文档中的字段名
        stageType,
        robotIds: selectedRobotIds // 添加选中的机器人ID列表
        // 注意：一键操作不传 courseId，表示对所有课程进行操作
      };

      console.log('Batch authorization params:', params);

      if (enable) {
        await courseService.enableAgentCourse(params);
        toastService.success(`已对 ${selectedRobotIds.length} 台机器人执行一键授权`);
      } else {
        await courseService.disableAgentCourse(params);
        toastService.success(`已对 ${selectedRobotIds.length} 台机器人执行一键关闭`);
      }

      // 这里可以触发数据刷新

    } catch (error) {
      const errorMessage = error instanceof ApiClientError
        ? error.message
        : `一键${enable ? '授权' : '关闭'}失败`;
      toastService.error(errorMessage);
      console.error(`Failed to batch ${enable ? 'enable' : 'disable'} courses:`, error);
    } finally {
      setBatchAuthorizing(false);
    }
  };

  // 处理批量控制跳转
  const handleBatchControl = () => {
    const selectedRobotIds = getSelectedRobotIds();

    if (selectedRobotIds.length === 0) {
      toastService.error('请先选择要控制的机器人');
      return;
    }

    // 从URL获取代理商信息
    const agentProvince = searchParams.get('agentProvince') || '';
    const agentCity = searchParams.get('agentCity') || '';
    const agentArea = searchParams.get('agentArea') || '';
    const agentName = searchParams.get('agentName') || '';
    const agentId = searchParams.get('agentId') || '';
    const stageType = searchParams.get('stageType') || 'UNKNOWN';

    // 使用BatchStorageManager存储批量数据
    let batchId: string;
    try {
      batchId = BatchStorageManager.storeBatchData(selectedRobotIds, {
        province: agentProvince,
        city: agentCity,
        area: agentArea,
        name: agentName,
        id: agentId,
        stageType
      });
    } catch (error) {
      toastService.error('存储批量数据失败，请重试');
      return;
    }

    // 构建跳转URL参数（不包含robotIds，使用batchId代替）
    const params = new URLSearchParams({
      agentProvince,
      agentCity,
      agentArea,
      agentName,
      agentId,
      agentBots: selectedRobotIds.length.toString(), // 机器人台数为选中的数量
      stageType,
      batchId, // 使用batchId代替robotIds
      action: 'batchControl'
    });

    // 跳转到代理商详情页
    router.push(`/courseware/agent/detail?${params.toString()}`);
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-white">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-red-400">{error}</div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* 顶部信息区域 */}
        <div className="w-full rounded-lg overflow-hidden bg-black/98 backdrop-blur-lg p-4">
          {/* 所有信息和操作按钮放在同一行 */}
          <div className="flex flex-wrap items-center gap-4">
            {/* 左侧信息区域 */}
            <div className="flex flex-wrap items-center gap-8 flex-1">
              {/* 公司信息 */}
              <div className="relative flex-shrink-0">
                <div
                  className={cn(
                    "flex items-center gap-2 h-8 px-2",
                    !agentInfo.company && "cursor-pointer hover:bg-white/10 rounded transition-colors"
                  )}
                  onClick={() => !agentInfo.company && setIsCompanyDialogOpen(true)}
                >
                  <AgentIcon
                    color={agentInfo.company ? "white" : "#ef4444"}
                    className="size-6"
                  />
                  <span className="text-sm text-white">
                    {agentInfo.company || "<暂无归属代理商>"}
                  </span>
                </div>
                <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
              </div>

              {/* 位置信息 */}
              <div className="relative flex-shrink-0">
                <div className="flex items-center gap-2 h-8 px-2">
                  <MapPin className="size-6 text-white" />
                  <span className="text-sm text-white">{agentInfo.location}</span>
                </div>
                <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
              </div>

              {/* 机器人数量 */}
              <div className="relative flex-shrink-0">
                <div className="flex items-center gap-2 h-8 px-2">
                  <RobotIcon color="white" className="size-6" />
                  <span className="text-sm text-white">{agentInfo.totalRobots}台</span>
                </div>
                <div className="absolute bottom-0 left-2 right-2 h-px bg-green-500" />
              </div>
            </div>
            
            {/* 右侧操作按钮 - 向中间移动 */}
            <div className="flex items-center gap-2 ml-auto mr-16">
              {/* 使用自定义样式覆盖按钮默认高度 */}
              <button
                onClick={handleBatchControl}
                className="inline-flex items-center justify-center rounded-full bg-amber-500 hover:bg-amber-600 text-white text-xs px-3 py-0.5 h-6 font-medium"
              >
                批量控制
              </button>
              <button
                onClick={() => handleBatchAuthorization(true)}
                disabled={batchAuthorizing}
                className={cn(
                  "inline-flex items-center justify-center rounded-full text-white text-xs px-3 py-0.5 h-6 font-medium transition-colors",
                  batchAuthorizing
                    ? "bg-gray-500 cursor-not-allowed"
                    : "bg-green-600 hover:bg-green-700"
                )}
              >
                {batchAuthorizing ? "授权中..." : "一键授权"}
              </button>
              <button
                onClick={() => handleBatchAuthorization(false)}
                disabled={batchAuthorizing}
                className={cn(
                  "inline-flex items-center justify-center rounded-full text-white text-xs px-3 py-0.5 h-6 font-medium transition-colors",
                  batchAuthorizing
                    ? "bg-gray-500 cursor-not-allowed"
                    : "bg-red-600 hover:bg-red-700"
                )}
              >
                {batchAuthorizing ? "关闭中..." : "一键关闭"}
              </button>
            </div>
          </div>
        </div>

        {/* 表格区域 */}
        <RobotsTable
          robots={robots}
          totalRobots={totalRobots}
          selectedRobots={selectedRobots}
          onSelectChange={handleSelectChange}
          onSelectAll={handleSelectAll}
        />
      </div>

      {/* 公司信息编辑弹窗 */}
      <CompanyEditDialog
        open={isCompanyDialogOpen}
        onOpenChange={setIsCompanyDialogOpen}
        currentAgentId={undefined}
        onConfirm={(agentId: number, agentName: string) => {
          setAgentInfo(prev => ({ ...prev, company: agentName }));
          console.log("保存公司信息:", { agentId, agentName });
        }}
      />
    </AppLayout>
  );
}
