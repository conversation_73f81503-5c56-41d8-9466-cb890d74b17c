"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/app-layout";
import { BatchStorageManager } from "@/lib/batch-storage";
import { CompanyEditDialog } from "@/components/company-edit-dialog";
import { toastService } from "@/services/toast";

import { RobotCoursesCard } from "@/components/robot-courses-card";

export default function AgentDetailPage() {
  const searchParams = useSearchParams();
  const [agentData, setAgentData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [batchRobotIds, setBatchRobotIds] = useState<string[]>([]);
  const [isCompanyDialogOpen, setIsCompanyDialogOpen] = useState(false);

  // 从URL获取参数并构建页面数据
  useEffect(() => {
    const buildAgentData = () => {
      try {
        setLoading(true);
        setError(null);

        // 从URL获取参数
        const agentId = searchParams.get('agentId') || '';
        const agentProvince = searchParams.get('agentProvince') || '';
        const agentCity = searchParams.get('agentCity') || '';
        const agentArea = searchParams.get('agentArea') || '';
        const agentName = searchParams.get('agentName') || '';
        const agentBots = searchParams.get('agentBots') || '';
        const agentCompositeId = searchParams.get('agentCompositeId') || '';
        const stageType = searchParams.get('stageType') as 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY' || 'UNKNOWN';
        const action = searchParams.get('action') || '';
        const batchId = searchParams.get('batchId') || '';

        // 如果是批量控制操作，从sessionStorage读取robotIds
        let batchRobotIdsList: string[] = [];
        if (batchId && action === 'batchControl') {
          const batchData = BatchStorageManager.getBatchData(batchId);
          if (batchData) {
            batchRobotIdsList = batchData.robotIds;
            setBatchRobotIds(batchRobotIdsList);
          }
        }


        // 确定使用的代理商信息
        let province = agentProvince;
        let city = agentCity;
        let area = agentArea;
        let finalAgentName = agentName;
        let finalAgentId = agentId;
        let finalBots = agentBots ? parseInt(agentBots) : 0;

        // 如果没有具体的代理商信息，尝试从组合ID解析
        if (!province && !city && !finalAgentName && agentCompositeId) {
          const parts = agentCompositeId.split('-');
          if (parts.length >= 3) {
            province = parts[0];
            city = parts[1];
            finalAgentName = parts.slice(2).join('-');
          }
        }

        // 调试信息
        console.log('agent detail page - URL parameters:', {
          province, city, area, finalAgentId, finalAgentName, finalBots, stageType, action
        });

        // 验证必要参数
        if (!finalAgentName || !province || !city) {
          setError('缺少必要的代理商信息');
          return;
        }

        // 判断合作商名称状态
        let displayCompany = finalAgentName;
        let showCompanyDialog = false;

        // 参考 agent-management.tsx 的逻辑判断合作商名称
        if (!finalAgentName || finalAgentName === 'unassigned' || finalAgentName === 'missingSn') {
          displayCompany = ''; // 设置为空，表示暂无归属代理商
          showCompanyDialog = true; // 允许点击弹窗
        }

        // 构建代理商数据（使用传递过来的信息，不需要API调用）
        const agentInfo = {
          company: displayCompany,
          location: `${province} ${city}`.trim(),
          totalRobots: finalBots, // 使用真实的机器人台数
          sn: "SN12345", // 默认值
          id: finalAgentId || agentCompositeId || "ID54321",
          isLocked: false,
          courses: [], // 初始为空，课程数据将通过课程授权组件获取
          showCompanyDialog: showCompanyDialog // 新增字段，控制是否显示公司弹窗
        };

        setAgentData(agentInfo);

      } catch (err) {
        const errorMessage = err instanceof Error
          ? err.message
          : '构建代理商数据失败';
        setError(errorMessage);
        console.error('Failed to build agent data:', err);
      } finally {
        setLoading(false);
      }
    };

    buildAgentData();
  }, [searchParams]);

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-white">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  // 错误已通过toast显示，不再需要单独的错误页面
  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-muted-foreground">暂无数据</div>
        </div>
      </AppLayout>
    );
  }

  if (!agentData) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-gray-400">暂无数据</div>
        </div>
      </AppLayout>
    );
  }

  // 获取代理商信息用于传递给组件
  const componentProvince = searchParams.get('agentProvince') || '';
  const componentCity = searchParams.get('agentCity') || '';
  const componentArea = searchParams.get('agentArea') || '';
  const componentAgentId = searchParams.get('agentId') || '';
  const componentStageType = searchParams.get('stageType') as 'UNKNOWN' | 'PRESCHOOL' | 'PRIMARY' || 'UNKNOWN';

  // 优先使用从sessionStorage读取的robotIds，如果没有则使用URL参数（向后兼容）
  const urlRobotIds = searchParams.get('robotIds') || '';
  const componentRobotIds = batchRobotIds.length > 0 ? batchRobotIds.join(',') : urlRobotIds;

  return (
    <AppLayout>
      <div className="space-y-6">
        <RobotCoursesCard
          agentData={agentData}
          province={componentProvince}
          city={componentCity}
          area={componentArea}
          agentId={componentAgentId}
          stageType={componentStageType}
          robotIds={componentRobotIds}
          onCompanyClick={() => agentData?.showCompanyDialog && setIsCompanyDialogOpen(true)}
        />
      </div>

      {/* 公司信息编辑弹窗 */}
      <CompanyEditDialog
        open={isCompanyDialogOpen}
        onOpenChange={setIsCompanyDialogOpen}
        currentCompany={agentData?.company || ''}
        onConfirm={(agentId: number, agentName: string) => {
          // 更新代理商信息
          setAgentData((prev: any) => ({ ...prev, company: agentName, showCompanyDialog: false }));
          console.log("保存公司信息:", { agentId, agentName });
          toastService.success(`已将代理商归属更新为: ${agentName}`);
        }}
        title="代理商归属信息变更"
        placeholder="请选择代理商"
        stageType={componentStageType}
      />
    </AppLayout>
  );
}
